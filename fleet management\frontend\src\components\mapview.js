import React, { useEffect, useState } from "react";
import { Google<PERSON>ap, Marker, useLoadScript } from "@react-google-maps/api";
import { fetchVehicles } from "../services/api";

const containerStyle = { width: "100%", height: "500px" };
const center = { lat: 12.9716, lng: 77.5946 }; // Example: Bangalore

export default function MapView() {
  const { isLoaded } = useLoadScript({ googleMapsApiKey: "YOUR_GOOGLE_MAPS_API_KEY" });
  const [vehicles, setVehicles] = useState([]);
  const [ws, setWs] = useState(null);

  useEffect(() => {
    fetchVehicles().then(setVehicles);

    const socket = new WebSocket("ws://localhost:5000");
    socket.onmessage = (message) => {
      const data = JSON.parse(message.data);
      if (data.type === "VEHICLE_UPDATED") {
        setVehicles((prev) =>
          prev.map((v) => (v._id === data.vehicle._id ? data.vehicle : v))
        );
      }
    };
    setWs(socket);
    return () => socket.close();
  }, []);

  if (!isLoaded) return <div>Loading Maps...</div>;

  return (
    <GoogleMap mapContainerStyle={containerStyle} center={center} zoom={10}>
      {vehicles.map((v) => (
        <Marker key={v._id} position={v.current_location} label={v.vehicle_number} />
      ))}
    </GoogleMap>
  );
}
