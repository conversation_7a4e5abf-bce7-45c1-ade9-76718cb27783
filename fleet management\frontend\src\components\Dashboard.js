import React, { useEffect, useState } from "react";
import { fetchVehicles } from "../services/api";

export default function DeliveryDashboard() {
  const [deliveries, setDeliveries] = useState([]);
  const [vehicles, setVehicles] = useState([]);

  useEffect(() => {
    fetch("/deliveries").then(res => res.json()).then(setDeliveries);
    fetchVehicles().then(setVehicles);
  }, []);

  const updateStatus = async (id, status) => {
    const res = await fetch(`/deliveries/${id}`, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ status })
    });
    const updated = await res.json();
    setDeliveries(deliveries.map(d => (d._id === id ? updated : d)));
  };

  return (
    <div>
      <h2>Deliveries</h2>
      <table>
        <thead>
          <tr><th>Customer</th><th>Status</th><th>Vehicle</th><th>Actions</th></tr>
        </thead>
        <tbody>
          {deliveries.map(d => (
            <tr key={d._id}>
              <td>{d.customer_name}</td>
              <td>{d.status}</td>
              <td>{d.assigned_vehicle_id?.vehicle_number || "Unassigned"}</td>
              <td>
                <button onClick={() => updateStatus(d._id, "in_transit")}>In Transit</button>
                <button onClick={() => updateStatus(d._id, "delivered")}>Delivered</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}