const mongoose = require("mongoose");

const DeliverySchema = new mongoose.Schema({
  customer_name: String,
  customer_address: String,
  assigned_vehicle_id: { type: mongoose.Schema.Types.ObjectId, ref: "Vehicle" },
  status: { type: String, enum: ["pending", "in_transit", "delivered"], default: "pending" },
  expected_delivery_time: Date,
  actual_delivery_time: Date
});

module.exports = mongoose.model("Delivery", DeliverySchema);