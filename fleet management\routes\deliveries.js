const express = require("express");
const router = express.Router();
const Delivery = require("../models/Delivery");

// Get all deliveries
router.get("/", async (req, res) => {
  const deliveries = await Delivery.find().populate("assigned_vehicle_id");
  res.json(deliveries);
});

// Create a new delivery
router.post("/", async (req, res) => {
  const delivery = new Delivery(req.body);
  await delivery.save();
  res.json(delivery);
});

// Update delivery status
router.put("/:id", async (req, res) => {
  const delivery = await Delivery.findByIdAndUpdate(req.params.id, req.body, { new: true });
  res.json(delivery);
});

module.exports = router;
